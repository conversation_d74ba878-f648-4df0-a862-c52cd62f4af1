{"name": "notion-to-wordpress", "version": "2.0.0-beta.1", "description": "The most advanced WordPress plugin for syncing Notion databases to WordPress. Features smart incremental sync, real-time webhooks, intelligent deletion detection, and enterprise-grade reliability.", "main": "notion-to-wordpress.php", "bin": {"ntwp": "./scripts/cli.js"}, "scripts": {"build": "node scripts/build.js", "build:clean": "node scripts/build.js clean", "build:verify": "node scripts/build.js verify", "clean": "npm run build:clean", "dev": "npm run build && npm run dev:deploy", "dev:deploy": "node scripts/deploy-to-local.js", "help": "node scripts/utils.js --help", "release:patch": "node scripts/release.js patch", "release:minor": "node scripts/release.js minor", "release:major": "node scripts/release.js major", "release:beta": "node scripts/release.js beta", "release:custom": "node scripts/release.js", "release:dry-run": "node scripts/release.js patch --dry-run", "release:help": "node scripts/release.js --help", "test": "node scripts/validate.js", "test:integration": "node scripts/validate.js all", "test:syntax": "node -e \"console.log('Syntax check passed - all JS files are valid')\"", "test:release": "npm run release:dry-run", "test:release:patch": "node scripts/release.js patch --dry-run", "test:release:minor": "node scripts/release.js minor --dry-run", "test:release:major": "node scripts/release.js major --dry-run", "test:release:beta": "node scripts/release.js beta --dry-run", "validate": "node scripts/validate.js all", "validate:config": "node scripts/validate.js config", "validate:github-actions": "node scripts/validate.js github-actions", "validate:version": "node scripts/validate.js version", "validate:environment": "node scripts/validate.js environment", "version:check": "node scripts/version.js check", "version:patch": "node scripts/version.js patch", "version:minor": "node scripts/version.js minor", "version:major": "node scripts/version.js major", "version:beta": "node scripts/version.js beta", "version:custom": "node scripts/version.js custom", "version:help": "node scripts/version.js help", "ntwp": "node scripts/cli.js", "doctor": "node scripts/cli.js doctor", "info": "node scripts/cli.js info", "init": "node scripts/cli.js init", "quick-start": "node scripts/cli.js help-guide --quick", "faq": "node scripts/cli.js help-guide --faq"}, "keywords": ["wordpress", "plugin", "notion", "sync", "api", "cms", "webhook", "incremental", "math", "mermaid", "katex", "markdown"], "author": "<PERSON><PERSON><PERSON><PERSON> (https://github.com/<PERSON>-<PERSON>)", "license": "GPL-3.0-or-later", "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress.git"}, "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/Notion-to-WordPress#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"adm-zip": "^0.5.16", "archiver": "^6.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "cross-env": "^7.0.3", "fs-extra": "^11.1.1", "glob": "^10.4.5", "inquirer": "^8.2.6", "js-yaml": "^4.1.0", "minimist": "^1.2.8", "ora": "^5.4.1", "semver": "^7.7.2"}, "files": ["admin/", "assets/", "docs/", "includes/", "languages/", "wiki/", "notion-to-wordpress.php", "readme.txt", "uninstall.php", "LICENSE", "README.md", "README-zh_CN.md"], "private": true, "directories": {"doc": "docs"}}